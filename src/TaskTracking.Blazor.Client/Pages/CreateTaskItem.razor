@page "/task-groups/{taskGroupId:guid}/tasks/create"
@inherits TaskTrackingComponentBase
@using Severity = MudBlazor.Severity
@using TaskTracking.TaskGroupAggregate.TaskItems

<div class="islamic-pattern-bg">
    <MudContainer MaxWidth="MaxWidth.Large" Class="py-4">
        <!-- Header Section -->
        <MudGrid Class="mb-4" AlignItems="Center">
            <MudItem xs="12" sm="8" md="9">
                <div class="d-flex align-items-center">
                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                   Color="Color.Primary"
                                   OnClick="@(() => NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}"))"
                                   Class="me-3" />
                    <div>
                        <MudText Typo="Typo.h4" Class="section-title mb-1">
                            <MudIcon Icon="@Icons.Material.Filled.Add" Class="me-2" />
                            @L["CreateTaskItem"]
                        </MudText>
                        <MudText Typo="Typo.body2" Class="text-muted d-none d-sm-block">
                            @L["CreateNewTaskItemDetails"]
                        </MudText>
                    </div>
                </div>
            </MudItem>
            
            <!-- Action Buttons -->
            <MudItem xs="12" sm="4" md="3">
                <MudStack Row="true" Spacing="2" Class="d-none d-sm-flex justify-end">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Visibility"
                               OnClick="@(() => NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}"))">
                        @L["ViewGroup"]
                    </MudButton>
                </MudStack>
                
                <!-- Mobile Buttons -->
                <MudStack Spacing="2" Class="d-block d-sm-none">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Visibility"
                               OnClick="@(() => NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}"))"
                               FullWidth="true">
                        @L["ViewGroup"]
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>

        @if (IsLoading)
        {
            <div class="text-center py-5">
                <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                <MudText Typo="Typo.body1" Class="mt-3">@L["LoadingTaskGroup"]</MudText>
            </div>
        }
        else if (TaskGroup == null)
        {
            <MudAlert Severity="Severity.Error" Class="mb-4">
                <MudText>@L["TaskGroupNotFound"]</MudText>
            </MudAlert>
        }
        else
        {
            <!-- Task Group Info -->
            <MudCard Class="islamic-card mb-4" Elevation="2">
                <MudCardContent Class="pa-4">
                    <MudText Typo="Typo.h6" Class="mb-2">
                        <MudIcon Icon="@Icons.Material.Filled.Group" Class="me-2" />
                        @TaskGroup.Title
                    </MudText>
                    <MudText Typo="Typo.body2" Class="text-muted">
                        @L["CreatingTaskForGroup"]
                    </MudText>
                </MudCardContent>
            </MudCard>

            <!-- Create Form -->
            <MudCard Class="islamic-card" Elevation="3">
                <MudCardContent Class="pa-6">
                    <MudForm Model="@CreateDto" @ref="@form" Validation="@(CreateTaskItemDtoValidator.ValidateValue)" ValidationDelay="0">
                        <!-- Title Field -->
                        <MudTextField @bind-Value="CreateDto.Title"
                                      Label="@L["Title"]"
                                      Variant="Variant.Outlined"
                                      Class="mb-4"
                                      Required="true"
                                      MaxLength="256"
                                      Counter="256"
                                      HelperText="@L["EnterTaskTitle"]"
                                      For="@(() => CreateDto.Title)"
                                      Immediate="true" />

                        <!-- Description Field -->
                        <MudGrid Class="mb-4" Spacing="10">
                            <MudItem xs="12" md="6">
                                <MudTextField @bind-Value="CreateDto.Description"
                                              Label="@L["Description"]"
                                              Variant="Variant.Outlined"
                                              Lines="8"
                                              Class="mb-4"
                                              Required="true"
                                              MaxLength="2000"
                                              Counter="2000"
                                              HelperText="@L["EnterTaskDescription"]"
                                              Immediate="true"
                                              For="@(() => CreateDto.Description)"/>
                            </MudItem>

                            <MudItem xs="12" md="6" Style="margin-top: 20px">
                                <div class="markdown-content">
                                    <MudMarkdown Value="@CreateDto.Description"/>
                                </div>
                            </MudItem>
                        </MudGrid>

                        <!-- Task Type Selection -->
                        <MudText Typo="Typo.h6" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Category" Class="me-2" />
                            @L["TaskType"]
                        </MudText>
                        
                        <MudRadioGroup @bind-SelectedOption="CreateDto.TaskType" Class="mb-4">
                            <MudRadio Option="TaskType.OneTime" Color="Color.Primary">
                                <div class="d-flex align-items-center">
                                    <MudIcon Icon="@Icons.Material.Filled.Event" Class="me-2" />
                                    <div>
                                        <MudText Typo="Typo.body1">@L["OneTimeTask"]</MudText>
                                        <MudText Typo="Typo.caption" Class="text-muted">@L["OneTimeTaskDescription"]</MudText>
                                    </div>
                                </div>
                            </MudRadio>
                            <MudRadio Option="TaskType.Recurring" Color="Color.Primary">
                                <div class="d-flex align-items-center">
                                    <MudIcon Icon="@Icons.Material.Filled.Repeat" Class="me-2" />
                                    <div>
                                        <MudText Typo="Typo.body1">@L["RecurringTask"]</MudText>
                                        <MudText Typo="Typo.caption" Class="text-muted">@L["RecurringTaskDescription"]</MudText>
                                    </div>
                                </div>
                            </MudRadio>
                        </MudRadioGroup>

                        <!-- Date Fields -->
                        <MudGrid Class="mb-4">
                            <MudItem xs="12" md="6">
                                <MudDatePicker @bind-Date="StartDatePicker"
                                               Label="@L["StartDate"]"
                                               Variant="Variant.Outlined"
                                               Required="true"
                                               DateFormat="dd/MM/yyyy"
                                               HelperText="@L["SelectStartDate"]"
                                               For="@(() => CreateDto.StartDate)" />
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudDatePicker @bind-Date="EndDatePicker"
                                               Label="@L["EndDate"]"
                                               Variant="Variant.Outlined"
                                               DateFormat="dd/MM/yyyy"
                                               Clearable="true"
                                               HelperText="@L["OptionalEndDate"]"
                                               For="@(() => CreateDto.EndDate)" />
                            </MudItem>
                        </MudGrid>

                        <!-- Recurrence Pattern Section (Conditional) -->
                        @if (CreateDto.TaskType == TaskType.Recurring)
                        {
                            <MudCard Class="mb-4" Elevation="1">
                                <MudCardHeader>
                                    <CardHeaderContent>
                                        <MudText Typo="Typo.h6">
                                            <MudIcon Icon="@Icons.Material.Filled.Repeat" Class="me-2" />
                                            @L["RecurrencePattern"]
                                        </MudText>
                                    </CardHeaderContent>
                                </MudCardHeader>
                                <MudCardContent>
                                    <!-- Recurrence Type -->
                                    <MudSelect @bind-Value="CreateDto.RecurrencePattern.RecurrenceType"
                                               Label="@L["RecurrenceType"]"
                                               Variant="Variant.Outlined"
                                               Class="mb-4"
                                               Required="true"
                                               For="@(() => CreateDto.RecurrencePattern.RecurrenceType)">
                                        <MudSelectItem Value="RecurrenceType.Daily">
                                            <div class="d-flex align-items-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Today" Class="me-2" />
                                                @L["Daily"]
                                            </div>
                                        </MudSelectItem>
                                        <MudSelectItem Value="RecurrenceType.Weekly">
                                            <div class="d-flex align-items-center">
                                                <MudIcon Icon="@Icons.Material.Filled.DateRange" Class="me-2" />
                                                @L["Weekly"]
                                            </div>
                                        </MudSelectItem>
                                        <MudSelectItem Value="RecurrenceType.Monthly">
                                            <div class="d-flex align-items-center">
                                                <MudIcon Icon="@Icons.Material.Filled.CalendarMonth" Class="me-2" />
                                                @L["Monthly"]
                                            </div>
                                        </MudSelectItem>
                                    </MudSelect>

                                    <!-- Interval -->
                                    <MudNumericField @bind-Value="CreateDto.RecurrencePattern.Interval"
                                                     Label="@L["Interval"]"
                                                     Variant="Variant.Outlined"
                                                     Class="mb-4"
                                                     Required="true"
                                                     Min="1"
                                                     Max="365"
                                                     HelperText="@GetIntervalHelperText()"
                                                     For="@(() => CreateDto.RecurrencePattern.Interval)" />

                                    <!-- Days of Week (for Weekly recurrence) -->
                                    @if (CreateDto.RecurrencePattern.RecurrenceType == RecurrenceType.Weekly)
                                    {
                                        <MudText Typo="Typo.subtitle1" Class="mb-2">@L["DaysOfWeek"]</MudText>
                                        <MudGrid Class="mb-4">
                                            @foreach (DayOfWeek day in Enum.GetValues<DayOfWeek>())
                                            {
                                                <MudItem xs="6" sm="4" md="3" lg="2">
                                                    <MudCheckBox @bind-Checked="@GetDayOfWeekChecked(day)"
                                                                 CheckedChanged="@((bool isChecked) => OnDayOfWeekChanged(day, isChecked))"
                                                                 Label="@L[day.ToString()]"
                                                                 Color="Color.Primary" />
                                                </MudItem>
                                            }
                                        </MudGrid>
                                    }

                                    <!-- End Condition -->
                                    <MudText Typo="Typo.subtitle1" Class="mb-2">@L["EndCondition"]</MudText>
                                    <MudRadioGroup @bind-SelectedOption="EndConditionType" Class="mb-4">
                                        <MudRadio Option="@("EndDate")" Color="Color.Primary">@L["EndDate"]</MudRadio>
                                        <MudRadio Option="@("Occurrences")" Color="Color.Primary">@L["NumberOfOccurrences"]</MudRadio>
                                    </MudRadioGroup>

                                    @if (EndConditionType == "EndDate")
                                    {
                                        <MudDatePicker @bind-Date="RecurrenceEndDatePicker"
                                                       Label="@L["RecurrenceEndDate"]"
                                                       Variant="Variant.Outlined"
                                                       DateFormat="dd/MM/yyyy"
                                                       HelperText="@L["SelectRecurrenceEndDate"]"
                                                       For="@(() => CreateDto.RecurrencePattern.EndDate)" />
                                    }
                                    else
                                    {
                                        <MudNumericField @bind-Value="CreateDto.RecurrencePattern.Occurrences"
                                                         Label="@L["NumberOfOccurrences"]"
                                                         Variant="Variant.Outlined"
                                                         Min="1"
                                                         Max="1000"
                                                         HelperText="@L["EnterNumberOfOccurrences"]"
                                                         For="@(() => CreateDto.RecurrencePattern.Occurrences)" />
                                    }
                                </MudCardContent>
                            </MudCard>
                        }

                        <!-- Information Card -->
                        <MudAlert Severity="Severity.Info" Class="mb-4">
                            <MudText Typo="Typo.body2">
                                <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Class="me-2" />
                                @L["CreateTaskItemInfo"]
                            </MudText>
                        </MudAlert>

                        <!-- Action Buttons -->
                        <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="d-none d-sm-flex">
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Default"
                                       StartIcon="@Icons.Material.Filled.Cancel"
                                       OnClick="@(() => NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}"))">
                                @L["Cancel"]
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       OnClick="@(async () => await Submit())"
                                       Disabled="@IsSubmitting">
                                @if (IsSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                                }
                                @L["CreateTaskItem"]
                            </MudButton>
                        </MudStack>

                        <!-- Mobile Action Buttons -->
                        <MudStack Spacing="2" Class="d-block d-sm-none">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       OnClick="@(async () => await Submit())"
                                       Disabled="@IsSubmitting"
                                       FullWidth="true">
                                @if (IsSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="me-2" />
                                }
                                @L["CreateTaskItem"]
                            </MudButton>
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Default"
                                       StartIcon="@Icons.Material.Filled.Cancel"
                                       OnClick="@(() => NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}"))"
                                       FullWidth="true">
                                @L["Cancel"]
                            </MudButton>
                        </MudStack>
                    </MudForm>
                </MudCardContent>
            </MudCard>
        }
    </MudContainer>
</div>
