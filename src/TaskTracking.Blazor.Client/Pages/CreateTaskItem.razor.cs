using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using TaskTracking.TaskGroupAggregate;
using TaskTracking.TaskGroupAggregate.Dtos.TaskGroups;
using TaskTracking.TaskGroupAggregate.Dtos.TaskItems;
using TaskTracking.TaskGroupAggregate.TaskItems;
using TaskTracking.TaskGroupAggregate.Validators;

namespace TaskTracking.Blazor.Client.Pages;

public partial class CreateTaskItem
{
    [Parameter] public Guid TaskGroupId { get; set; }
    [Inject] private ITaskGroupAppService TaskGroupAppService { get; set; } = null!;
    [Inject] private NavigationManager NavigationManager { get; set; } = null!;
    [Inject] private ISnackbar Snackbar { get; set; } = null!;
    [Inject] private CreateTaskItemDtoValidator CreateTaskItemDtoValidator { get; set; } = null!;

    private TaskGroupDto? TaskGroup { get; set; }
    private CreateTaskItemDto CreateDto { get; set; } = new();
    private bool IsLoading { get; set; } = true;
    private bool IsSubmitting { get; set; } = false;
    
    // Date picker properties
    private DateTime? StartDatePicker { get; set; }
    private DateTime? EndDatePicker { get; set; }
    private DateTime? RecurrenceEndDatePicker { get; set; }
    
    // Recurrence properties
    private string EndConditionType { get; set; } = "EndDate";
    
    private MudForm form;

    protected override async Task OnInitializedAsync()
    {
        await LoadTaskGroup();
        InitializeCreateDto();
        await base.OnInitializedAsync();
    }

    private async Task LoadTaskGroup()
    {
        try
        {
            IsLoading = true;
            TaskGroup = await TaskGroupAppService.GetAsync(TaskGroupId);
        }
        catch (Exception ex)
        {
            Snackbar.Add(L["ErrorLoadingTaskGroup"], Severity.Error);
            Console.WriteLine($"Error loading task group: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void InitializeCreateDto()
    {
        // Initialize with default values
        StartDatePicker = DateTime.Today;
        CreateDto.StartDate = DateTime.Today;
        CreateDto.TaskType = TaskType.OneTime;
        CreateDto.RecurrencePattern = new CreateRecurrencePatternDto
        {
            RecurrenceType = RecurrenceType.Daily,
            Interval = 1,
            DaysOfWeek = new List<DayOfWeek>()
        };
    }

    private async Task Submit()
    {
        // Update DTO with date picker values
        if (StartDatePicker.HasValue)
        {
            CreateDto.StartDate = StartDatePicker.Value;
        }
        CreateDto.EndDate = EndDatePicker;

        // Handle recurrence pattern
        if (CreateDto.TaskType == TaskType.Recurring)
        {
            if (EndConditionType == "EndDate")
            {
                CreateDto.RecurrencePattern.EndDate = RecurrenceEndDatePicker;
                CreateDto.RecurrencePattern.Occurrences = null;
            }
            else
            {
                CreateDto.RecurrencePattern.EndDate = null;
                // Occurrences is already bound to the numeric field
            }
        }
        else
        {
            CreateDto.RecurrencePattern = null;
        }

        await form.Validate();

        var result = await CreateTaskItemDtoValidator.ValidateAsync(CreateDto);

        result.Errors.ForEach(error =>
        {
            Snackbar.Add(error.ErrorMessage, Severity.Error);
        });

        if (form.IsValid && result.IsValid)
        {
            try
            {
                IsSubmitting = true;
                await TaskGroupAppService.CreateTaskItemAsync(TaskGroupId, CreateDto);
                
                Snackbar.Add(L["TaskItemCreatedSuccessfully"], Severity.Success);
                NavigationManager.NavigateTo($"/task-groups/{TaskGroupId}");
            }
            catch (Exception ex)
            {
                Snackbar.Add(L["ErrorCreatingTaskItem"], Severity.Error);
                Console.WriteLine($"Error creating task item: {ex.Message}");
            }
            finally
            {
                IsSubmitting = false;
            }
        }
    }

    private string GetIntervalHelperText()
    {
        return CreateDto.RecurrencePattern?.RecurrenceType switch
        {
            RecurrenceType.Daily => L["IntervalDaysHelperText"],
            RecurrenceType.Weekly => L["IntervalWeeksHelperText"],
            RecurrenceType.Monthly => L["IntervalMonthsHelperText"],
            _ => L["IntervalHelperText"]
        };
    }

    private bool GetDayOfWeekChecked(DayOfWeek day)
    {
        return CreateDto.RecurrencePattern?.DaysOfWeek?.Contains(day) ?? false;
    }

    private void OnDayOfWeekChanged(DayOfWeek day, bool isChecked)
    {
        if (CreateDto.RecurrencePattern?.DaysOfWeek == null)
        {
            CreateDto.RecurrencePattern.DaysOfWeek = new List<DayOfWeek>();
        }

        if (isChecked)
        {
            if (!CreateDto.RecurrencePattern.DaysOfWeek.Contains(day))
            {
                CreateDto.RecurrencePattern.DaysOfWeek.Add(day);
            }
        }
        else
        {
            CreateDto.RecurrencePattern.DaysOfWeek.Remove(day);
        }
    }
}
